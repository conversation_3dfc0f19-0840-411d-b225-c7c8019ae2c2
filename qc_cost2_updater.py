#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
千川数据更新脚本
将PHP的getQcCost2方法及其相关方法转换为Python脚本
"""

import requests
import json
import pymysql
import hashlib
from datetime import datetime, timedelta
import time
import math
from typing import Dict, List, Any, Optional, Tuple


class QcCost2Updater:
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化数据库连接配置
        
        Args:
            db_config: 数据库配置字典
        """
        self.db_config = db_config
        self.session = requests.Session()
        self.session.verify = False
        
    def get_db_connection(self):
        """获取数据库连接"""
        return pymysql.connect(
            host=self.db_config['hostname'],
            user=self.db_config['username'],
            password=self.db_config['password'],
            database=self.db_config['database'],
            port=self.db_config['hostport'],
            charset=self.db_config['charset'],
            autocommit=True
        )
    
    def db_query(self, sql: str, params: tuple = None) -> List[Dict]:
        """执行数据库查询"""
        conn = self.get_db_connection()
        try:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        finally:
            conn.close()
    
    def db_execute(self, sql: str, params: tuple = None) -> int:
        """执行数据库更新/插入操作"""
        conn = self.get_db_connection()
        try:
            with conn.cursor() as cursor:
                rows = cursor.execute(sql, params)
                conn.commit()
                return rows
        finally:
            conn.close()
    
    def db_insert(self, table: str, data: Dict[str, Any]) -> int:
        """插入数据"""
        fields = ', '.join(data.keys())
        placeholders = ', '.join(['%s'] * len(data))
        sql = f"INSERT INTO {table} ({fields}) VALUES ({placeholders})"
        return self.db_execute(sql, tuple(data.values()))
    
    def db_update(self, table: str, data: Dict[str, Any], where: str, where_params: tuple) -> int:
        """更新数据"""
        set_clause = ', '.join([f"{k} = %s" for k in data.keys()])
        sql = f"UPDATE {table} SET {set_clause} WHERE {where}"
        params = tuple(data.values()) + where_params
        return self.db_execute(sql, params)
    
    def db_delete(self, table: str, where: str, where_params: tuple) -> int:
        """删除数据"""
        sql = f"DELETE FROM {table} WHERE {where}"
        return self.db_execute(sql, where_params)
    
    def get_access_token(self, token_id: int) -> str:
        """获取访问令牌"""
        sql = "SELECT cookie FROM bt_admin_cookie WHERE id = %s"
        result = self.db_query(sql, (token_id,))
        return result[0]['cookie'] if result else None
    
    def get_tenant_access_token(self) -> str:
        """获取飞书租户访问令牌"""
        sql = "SELECT cookie FROM bt_admin_cookie WHERE type = '13'"
        result = self.db_query(sql)
        return result[0]['cookie'] if result else None
    
    def to_message(self, phone: str, shop: Dict[str, Any], card: str = '') -> Dict[str, Any]:
        """发送飞书消息"""
        tenant_access_token = self.get_tenant_access_token()
        headers = {
            'Authorization': f'Bearer {tenant_access_token}'
        }
        
        # 获取用户ID
        body = {
            'mobiles': [phone],
            'include_resigned': True
        }
        
        response = self.session.post(
            'https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id?user_id_type=user_id',
            headers=headers,
            json=body
        )
        
        data = response.json()
        user_id = data['data']['user_list'][0]['user_id']
        
        # 生成UUID
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        uuid_str = f"{current_time}{shop}{card}"
        uuid = hashlib.md5(uuid_str.encode()).hexdigest()
        
        # 构建消息内容
        if card == 'AAqjPV3C50X8w':
            content = {
                'type': 'template',
                'data': {
                    'template_id': card,
                    'template_variable': {
                        'time': shop['time'],
                        'type': shop['type'],
                    },
                },
            }
        else:
            content = {}
        
        body = {
            'receive_id': user_id,
            'msg_type': 'interactive',
            'content': json.dumps(content),
            'uuid': uuid,
        }
        
        response = self.session.post(
            'https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=user_id',
            headers=headers,
            json=body
        )
        
        return response.json()
    
    def type_id(self, start: str, end: str) -> str:
        """更新账户是否存在全域ID"""
        sql = """
            SELECT * FROM bt_crm_cost2 
            WHERE crm_duweee >= %s AND crm_duweee <= %s
        """
        data = self.db_query(sql, (start, end))
        
        for item in data:
            if item['crm_ehudfb'] == '千川全站推广ROI2':
                self.db_update(
                    'bt_crm_cost2flow_qcid',
                    {'status': '3'},
                    'status <> %s AND ad_id = %s',
                    ('5', item['crm_koklbg'])
                )
        
        return 'ok'
    
    def update_cost2(self, start: str, end: str) -> Dict[str, Any]:
        """更新千川标准流水"""
        start_date = datetime.strptime(start, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
        end_date = datetime.strptime(end, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
        
        access_token = self.get_access_token(6)
        headers = {
            'Content-Type': 'application/json',
            'Access-Token': access_token
        }
        
        page = 1
        while True:
            body = {
                "agent_id": 1702252204088334,
                "start_date": start_date,
                "end_date": end_date,
                "page": page,
                "page_size": 100,
                "order_type": "DESC"
            }
            
            response = self.session.post(
                'https://api.oceanengine.com/open_api/2/agent/adv/cost_report/list/query/',
                headers=headers,
                json=body
            )
            
            data = response.json()
            
            if data['code'] == 0:
                for v in data['data']['list']:
                    cost2_data = {
                        'crm_rjdwbs': v['project_name'],  # 项目名称
                        'crm_ukhdva': '直播' if v['promotion_type'] == 1 else '短视频',  # 业务类型
                        'crm_duweee': v['attribution_date'],  # 时间
                        'crm_koklbg': v['adv_id'],  # 广告主账户id
                        'crm_nnbfmr': v['adv_name'],  # 广告主账户名称
                        'crm_pspngx': v['customer_id'],  # 客户ID
                        'crm_srslcm': v['customer_name'],  # 客户名称
                        'crm_pmaloa': v['sale_id'] if v['sale_id'] != 0 else None,  # 销售ID
                        'crm_kgnpwd': v['sale_name'],  # 销售名称
                        'crm_bhtyix': v['saleDepId'],  # 销售部门ID
                        'crm_gtgdma': v['saleDepName'],  # 销售部门名称
                        'crm_wovtcc': v['first_industry'],  # 客户一级行业
                        'crm_qhteke': v['second_industry'],  # 客户二级行业
                        'crm_olmnmp': int(datetime.strptime(v['register_time'], '%Y-%m-%d %H:%M:%S').timestamp()),  # 注册时间
                        'crm_dgthsv': v['second_agent_id'] if v['second_agent_id'] != 0 else None,  # 代理子账户ID
                        'crm_encvni': v['second_agent_name'],  # 代理子账户名称
                        'crm_zovozu': v['first_agent_id'],  # 一级代理商账户ID
                        'crm_upkcbr': v['first_agent_name'],  # 一级代理商账户名称
                        'crm_hbcayv': v['agent_id'],  # 一级代理商账户名称
                        'crm_owjagq': v['agent_name'],  # 一级代理商账户名称
                        'crm_jajerz': v['project_serial'],  # 项目编号
                        'crm_hcbkjo': v['project_name'],  # 千川项目名称
                        'crm_gzyiyo': '是' if v['system_origin'] != 0 else '否',  # 是否放心购
                        'crm_yoozrw': v['cost_source_name'],  # 消耗来源
                        'crm_czwygc': self._get_app_name(v['app_name']),  # 应用名称
                        'crm_ehudfb': '千川全站推广ROI2' if v['business_type_name'] == '千川全域搜索抢首屏' else v['business_type_name'],  # 业务来源
                        'crm_hcuwei': v['pricing_category_name'],  # 广告类型
                        'crm_czfnej': v['sub_shared_wallet_id'] if v['sub_shared_wallet_id'] != 0 else None,  # 共享子钱包id
                        'crm_pujgro': v['sub_shared_wallet_name'],  # 共享子钱包名称
                        'crm_osmncl': v['cost'] / 100000,  # 总消耗
                        'crm_jjdrjz': v['no_grant_cost'] / 100000,  # 非赠款消耗
                        'crm_klfbwc': v['prepay_cost'] / 100000,  # 预付消耗
                        'crm_bhxctv': v['credit_cost'] / 100000,  # 授信消耗
                        'crm_vkawnm': v['grant_cost'] / 100000,  # 赠款消耗
                        'crm_bayacv': v['sub_shared_wallet_cost'] / 100000,  # 共享钱包消耗
                        'crm_ypsspr': v['sub_shared_wallet_prepay_cost'] / 100000,  # 共享预付消耗
                        'crm_dzkkqz': v['sub_shared_wallet_credit_cost'] / 100000,  # 共享授信消耗
                        'crm_titdch': v['agent_performance_cost'] / 100000,  # 代理商业绩消耗
                        'crm_amuvlm': v['agent_performance_grant_cost'] / 100000,  # 赠款消耗（计返点）
                        'crm_dkfopl': v['customer_performance_grant_cost'] / 100000,  # 赠款消耗（计返货）
                        'crm_yjswdz': v['spu_label_name'],  # 品牌广告类型
                        'crm_tryzmx': v['brand_product_second_type_name'],  # 品牌二级类别名称
                        'crm_ltgwke': v['spu_id'],  # 商品ID
                        'crm_lqkskg': v['spu_name'],  # 商品名称
                        'crm_zmhxkn': self._get_settlement_stats_type(v['settlement_stats_type']),  # 结算统计类型
                        'crm_pbvgmw': v['settlement_first_industry_name'],  # 结算一级行业
                        'crm_xlwezd': v['settlement_second_industry_name'],  # 结算二级行业
                        'crm_npxqpi': v['settlement_internal_industry_category'],  # 结算行业内部行业分类
                        'crm_kvsmmk': '是' if v['is_matching_province'] == 1 else '否',  # 是否本地投放
                        'crm_dybeqd': v['cashback_type_name'],  # 资源包类型
                        'crm_emutyu': '直播计划' if v['promotion_type'] == 1 else '非直播计划',
                        'crm_fxnguy': self._get_ecommerce_type(v['ecommerce_type']),  # 电商类型
                        'crm_vwgxux': v['cpt_name'],  # 是否CPT
                        'crm_ihpffr': v['feedslive_name'],  # 是否FeedsLive
                    }
                    
                    self.db_insert('bt_crm_cost2', cost2_data)
                
                page += 1
                
                # 检查是否还有更多页
                total_pages = math.ceil(data['data']['page_info']['total_number'] / 100)
                if page > total_pages:
                    break
            else:
                return {
                    'code': 1,
                    'msg': f'千川流水数据更新失败，{data["message"]}'
                }
        
        return {
            'code': 0,
            'msg': f'千川流水数据更新成功，{data["message"]}'
        }
    
    def _get_app_name(self, app_name: int) -> Optional[str]:
        """获取应用名称"""
        app_names = {
            1: '通投智选',
            2: '西瓜APP',
            3: '搜索广告',
            4: '头条APP',
            5: '火山APP'
        }
        return app_names.get(app_name)
    
    def _get_settlement_stats_type(self, settlement_stats_type: int) -> Optional[str]:
        """获取结算统计类型"""
        types = {
            1: '行业类目',
            2: '引流电商',
            3: '白名单',
            4: '任务激励',
            5: '微信加粉'
        }
        return types.get(settlement_stats_type)
    
    def _get_ecommerce_type(self, ecommerce_type: int) -> Optional[str]:
        """获取电商类型"""
        types = {
            1: '闭环电商',
            2: '非闭环电商',
            3: '引流电商',
            4: '平台电商',
            5: '非电商'
        }
        return types.get(ecommerce_type)
    
    def update_cost2_bdt(self, start: str, end: str) -> Dict[str, Any]:
        """更新千川流水本地推"""
        access_token = self.get_access_token(38)
        headers = {
            'Content-Type': 'application/json; charset=utf-8',
            'Cookie': access_token
        }
        
        qc_data = []
        page = 1
        first_agent_ids = '1764299977280589'
        
        while True:
            url = f'https://agent.oceanengine.com/agent/settlement/adv/cost/?firstAgentIds={first_agent_ids}&endDate={end}&startDate={start}&page={page}&size=100'
            
            response = self.session.get(url, headers=headers)
            data = response.json()
            
            if data['code'] == 0:
                qc_data.extend(data['data']['data'])
                page += 1
                
                total_pages = math.ceil(data['data']['total'] / 100)
                if page > total_pages:
                    break
            else:
                return {
                    'code': 1,
                    'msg': f'本地推数据获取失败: {data["message"]}'
                }
        
        # 处理数据并插入数据库
        for v in qc_data:
            cost2_data = {
                'crm_rjdwbs': v.get('project_name', ''),
                'crm_ukhdva': '直播' if v.get('promotion_type') == 1 else '短视频',
                'crm_duweee': v.get('attribution_date', ''),
                'crm_koklbg': v.get('adv_id', ''),
                'crm_nnbfmr': v.get('adv_name', ''),
                'crm_ehudfb': '巨量本地推',
                'crm_osmncl': float(v.get('cost', 0)) / 100000,
                'crm_vkawnm': float(v.get('grant_cost', 0)) / 100000,
                # 添加其他必要字段...
            }
            
            self.db_insert('bt_crm_cost2', cost2_data)
        
        return {
            'code': 0,
            'msg': '本地推数据更新成功'
        }
    
    def update_cost2flow(self, start: str, end: str) -> Dict[str, Any]:
        """更新千川明细标准推广"""
        client = requests.Session()
        client.timeout = 30
        
        max_retries = 5
        initial_delay = 10
        base_delay = 2
        request_counter = 0
        
        url = 'https://api.oceanengine.com/open_api/v1.0/qianchuan/report/custom/get/'
        access_token = self.get_access_token(6)
        headers = {
            'Content-Type': 'application/json',
            'Access-Token': access_token,
        }
        
        try:
            sql_time = datetime.strptime(start, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            sql = """
                SELECT crm_koklbg, crm_nnbfmr, MAX(cost2_id) as cost2_id
                FROM bt_crm_cost2
                WHERE crm_ehudfb <> '千川全站推广ROI2'
                AND crm_duweee >= %s
                GROUP BY crm_koklbg, crm_nnbfmr
            """
            sql_data = self.db_query(sql, (sql_time,))
            
            for index, id_name in enumerate(sql_data):
                # 广告主之间的延迟
                if index > 0:
                    ad_delay = base_delay * (request_counter % 5 + 1)
                    print(f"广告主切换延迟: {ad_delay}秒")
                    time.sleep(ad_delay)
                
                page = 1
                page_success = True
                
                while page_success:
                    query_params = {
                        'advertiser_id': id_name['crm_koklbg'],
                        'data_topic': 'ECP_BASIC_DATA',
                        'dimensions': json.dumps(['marketing_goal', 'anchor_show_id', 'anchor_name', 'stat_time_day']),
                        'metrics': json.dumps(['stat_cost', 'pay_order_count', 'stat_pay_order_amount', 'indirect_order_pay_count_7days',
                                             'stat_indirect_order_pay_gmv_7days', 'stat_pay_order_coupon_amount', 'prepay_and_pay_order_roi',
                                             'all_order_prepay_and_pay_roi_7days']),
                        'filters': json.dumps([]),
                        'start_time': start,
                        'end_time': end,
                        'order_by': json.dumps([
                            {'type': 1, 'field': 'stat_time_day'},
                            {'type': 2, 'field': 'stat_cost'},
                            {'type': 2, 'field': 'pay_order_count'},
                            {'type': 2, 'field': 'stat_pay_order_amount'}
                        ]),
                        'page': page,
                        'page_size': 100,
                    }
                    
                    # 请求重试逻辑
                    success = False
                    retries = 0
                    delay = initial_delay
                    
                    while not success and retries <= max_retries:
                        try:
                            if retries > 0:
                                print(f"重试请求: 广告主ID={id_name['crm_koklbg']}, 页码={page}, 延迟={delay}秒")
                                time.sleep(delay)
                                delay = min(delay * 1.5, 60)
                            
                            response = client.get(url, params=query_params, headers=headers)
                            data = response.json()
                            
                            if data['code'] == 0:
                                for qc in data['data']['rows']:
                                    cost2flow = {
                                        'crm_adnixt': id_name['crm_nnbfmr'],
                                        'crm_wwajxo': id_name['crm_koklbg'],
                                        'crm_nkariq': qc['dimensions']['anchor_name'],
                                        'crm_kbkoun': qc['dimensions']['anchor_show_id'],
                                        'crm_ernibr': qc['dimensions']['stat_time_day'],
                                        'crm_kvdrmw': qc['metrics']['stat_cost'].replace(',', ''),
                                        'crm_lnqihn': qc['metrics']['pay_order_count'],
                                        'crm_hykbfa': qc['metrics']['stat_pay_order_amount'].replace(',', ''),
                                        'crm_kdmhmb': qc['metrics']['indirect_order_pay_count_7days'],
                                        'crm_ontwrp': qc['metrics']['stat_indirect_order_pay_gmv_7days'].replace(',', ''),
                                        'crm_fvhnup': qc['metrics']['stat_pay_order_coupon_amount'].replace(',', ''),
                                        'crm_zjlwrq': float(qc['metrics']['stat_indirect_order_pay_gmv_7days'].replace(',', '')) + float(qc['metrics']['stat_pay_order_amount'].replace(',', '')),
                                        'crm_eywvmm': qc['metrics']['prepay_and_pay_order_roi'],
                                        'crm_wsmzrg': qc['metrics']['all_order_prepay_and_pay_roi_7days'],
                                        'crm_psjcjw': "标准推广",
                                        'crm_gntuji': qc['dimensions']['marketing_goal']
                                    }
                                    
                                    if float(cost2flow['crm_kvdrmw']) != 0:
                                        self.db_insert('bt_crm_cost2flow', cost2flow)
                                
                                page += 1
                                success = True
                                request_counter += 1
                                
                                # 每10个请求后增加额外延迟
                                if request_counter % 10 == 0:
                                    print("每10次请求后额外延迟: 5秒")
                                    time.sleep(5)
                                
                                # 检查是否还有更多页
                                if page > data['data']['pagination']['total_page']:
                                    break
                                    
                            elif data['code'] == 40003:
                                retries += 1
                                print(f"触发限流: 广告主ID={id_name['crm_koklbg']}, 页码={page}, 错误信息={data['message']}")
                                delay = max(delay * 2, 30)
                            else:
                                return {
                                    'code': 1,
                                    'msg': f'明细标准推广获取失败: {data["message"]}'
                                }
                                
                        except requests.exceptions.RequestException as e:
                            retries += 1
                            print(f"请求异常: 广告主ID={id_name['crm_koklbg']}, 页码={page}, 错误信息={e}")
                            
                            if retries > max_retries:
                                page_success = False
                                break
                    
                    if not success:
                        return {
                            'code': 1,
                            'msg': '达到最大重试次数，请求失败'
                        }
            
            return {
                'code': 0,
                'msg': '明细标准推广更新成功'
            }
            
        except Exception as e:
            return {
                'code': 1,
                'msg': f'系统异常: {str(e)}'
            }
    
    def update_cos2flow_qy(self, start: str, end: str) -> Dict[str, Any]:
        """更新明细全域推广"""
        sql_time = datetime.strptime(start, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
        
        sql = """
            SELECT crm_koklbg, crm_nnbfmr, MAX(cost2_id) as cost2_id
            FROM bt_crm_cost2
            WHERE crm_ehudfb = '千川全站推广ROI2'
            AND crm_duweee >= %s
            GROUP BY crm_koklbg, crm_nnbfmr
        """
        sql_data = self.db_query(sql, (sql_time,))
        
        access_token = self.get_access_token(6)
        
        for id_name in sql_data:
            qy_id = self.qc_cost2flow_qy_id(id_name['crm_koklbg'], access_token)
            if qy_id['code'] == 1:
                qy_id['errId'] = id_name['crm_koklbg']
                return qy_id
            
            result = self.qc_cost2flow_qy_data(qy_id['all'], id_name['crm_koklbg'], start, end, access_token)
            if result['code'] == 1:
                return result
        
        return {
            'code': 0,
            'msg': '明细全域推广更新成功'
        }
    
    def qc_cost2flow_qy_id(self, ad_id: str, access_token: str) -> Dict[str, Any]:
        """获取全域抖音ID"""
        client = requests.Session()
        url = 'https://api.oceanengine.com/open_api/v1.0/qianchuan/uni_aweme/authorized/get/'
        headers = {
            'Content-Type': 'application/json',
            'Access-Token': access_token,
        }
        
        try:
            query_params = {
                'advertiser_id': ad_id,
                'page': 1,
                'page_size': 100,
            }
            
            response = client.get(url, params=query_params, headers=headers)
            data = response.json()
            
            if data['code'] == 0:
                return {
                    'code': 0,
                    'msg': f'全域抖音id获取成功{data["message"]}',
                    'all': data['data']['aweme_id_list']
                }
            else:
                return {
                    'code': 1,
                    'msg': f'全域抖音id获取失败{data["message"]}{ad_id}',
                }
                
        except requests.exceptions.RequestException as e:
            return {
                'code': 1,
                'msg': str(e)
            }
    
    def qc_cost2flow_qy_data(self, anchor_ids: List[str], ad_id: str, start: str, end: str, access_token: str) -> Dict[str, Any]:
        """获取全域推广数据"""
        client = requests.Session()
        url = 'https://api.oceanengine.com/open_api/v1.0/qianchuan/report/uni_promotion/dimension_data/author/get/'
        headers = {
            'Content-Type': 'application/json',
            'Access-Token': access_token,
        }
        
        try:
            for anchor_id in anchor_ids:
                query_params = {
                    'advertiser_id': ad_id,
                    'aweme_id': anchor_id,
                    'start_time': start,
                    'end_time': end,
                    'dimensions': json.dumps(['stat_time_day']),
                    'metrics': json.dumps(['stat_cost', 'pay_order_count', 'stat_pay_order_amount']),
                    'page': 1,
                    'page_size': 100,
                }
                
                response = client.get(url, params=query_params, headers=headers)
                data = response.json()
                
                if data['code'] == 0:
                    for row in data['data']['rows']:
                        cost2flow = {
                            'crm_adnixt': '',  # 需要从其他地方获取
                            'crm_wwajxo': ad_id,
                            'crm_nkariq': '',  # 需要从其他地方获取
                            'crm_kbkoun': anchor_id,
                            'crm_ernibr': row['dimensions']['stat_time_day'],
                            'crm_kvdrmw': row['metrics']['stat_cost'],
                            'crm_lnqihn': row['metrics']['pay_order_count'],
                            'crm_hykbfa': row['metrics']['stat_pay_order_amount'],
                            'crm_psjcjw': '全域推广',
                            'crm_gntuji': '推商品',  # 或 '推直播间'
                        }
                        
                        self.db_insert('bt_crm_cost2flow', cost2flow)
                else:
                    return {
                        'code': 1,
                        'msg': f'全域推广数据获取失败: {data["message"]}'
                    }
            
            return {
                'code': 0,
                'msg': '全域推广数据更新成功'
            }
            
        except Exception as e:
            return {
                'code': 1,
                'msg': f'系统异常: {str(e)}'
            }
    
    def qc_up_data(self, start: str, end: str) -> str:
        """更新明细赠款数据"""
        # 本地推广数据
        bd_data = self.db_query("""
            SELECT * FROM bt_crm_cost2flow
            WHERE crm_kbkoun IS NULL
            AND crm_ernibr >= %s AND crm_ernibr <= %s
        """, (start, end))
        
        bd_result = self.db_query("""
            SELECT crm_duweee, crm_koklbg, COUNT(*) AS count, SUM(crm_vkawnm) AS total
            FROM bt_crm_cost2
            WHERE crm_ehudfb = '巨量本地推'
            AND crm_duweee >= %s AND crm_duweee <= %s
            GROUP BY crm_duweee, crm_koklbg
        """, (start, end))
        
        bd_money_data = self.db_query("""
            SELECT crm_ernibr, crm_wwajxo, COUNT(*) AS count, SUM(crm_kvdrmw) AS total
            FROM bt_crm_cost2flow
            WHERE crm_psjcjw = '标准推广'
            AND crm_kbkoun IS NULL
            AND crm_ernibr >= %s AND crm_ernibr <= %s
            GROUP BY crm_ernibr, crm_wwajxo
        """, (start, end))
        
        # 全域推广数据
        qy_data2 = self.db_query("""
            SELECT * FROM bt_crm_cost2flow
            WHERE crm_psjcjw = '全域推广'
            AND crm_kbkoun <> ''
            AND crm_ernibr >= %s AND crm_ernibr <= %s
            AND crm_gntuji = '推商品'
        """, (start, end))
        
        qy_result2 = self.db_query("""
            SELECT crm_duweee, crm_koklbg, COUNT(*) AS count, SUM(crm_vkawnm) AS total
            FROM bt_crm_cost2
            WHERE crm_ehudfb = '千川全站推广ROI2'
            AND crm_ehudfb <> '巨量本地推'
            AND crm_emutyu = '非直播计划'
            AND crm_duweee >= %s AND crm_duweee <= %s
            GROUP BY crm_duweee, crm_koklbg
        """, (start, end))
        
        qy_money_data2 = self.db_query("""
            SELECT crm_ernibr, crm_wwajxo, COUNT(*) AS count, SUM(crm_kvdrmw) AS total
            FROM bt_crm_cost2flow
            WHERE crm_psjcjw = '全域推广'
            AND crm_kbkoun <> ''
            AND crm_gntuji = '推商品'
            AND crm_ernibr >= %s AND crm_ernibr <= %s
            GROUP BY crm_ernibr, crm_wwajxo
        """, (start, end))
        
        # 处理数据
        self.process_promotion_data(bd_data, bd_result, bd_money_data)
        self.process_promotion_data(qy_data2, qy_result2, qy_money_data2)
        
        # 添加其他数据处理...
        
        return '成功更新赠款'
    
    def process_promotion_data(self, data: List[Dict], result: List[Dict], money_data: List[Dict]):
        """处理推广数据"""
        groups = {}
        for item in data:
            key = f"{item['crm_wwajxo']}_{item['crm_ernibr']}"
            if key not in groups:
                groups[key] = []
            groups[key].append(item)
        
        for group_items in groups.values():
            yjkuoh_sum = 0
            last_item = group_items[-1]
            
            for index, item in enumerate(group_items[:-1]):
                x = self.find_total_by_date_and_account(result, item['crm_wwajxo'], item['crm_ernibr'], 'cost2')
                y = self.find_total_by_date_and_account(money_data, item['crm_wwajxo'], item['crm_ernibr'], 'cost2flow')
                
                if y != 0:
                    z = round(x * (float(item['crm_kvdrmw']) / y), 2)
                    w = round(float(item['crm_kvdrmw']) - z, 2)
                    yjkuoh_sum += z
                    
                    self.db_update(
                        'bt_crm_cost2flow',
                        {
                            'crm_ccftvr': w,
                            'crm_yjkuoh': z
                        },
                        'cost2flow_id = %s',
                        (item['cost2flow_id'],)
                    )
            
            # 处理最后一个项目
            x_last = self.find_total_by_date_and_account(result, last_item['crm_wwajxo'], last_item['crm_ernibr'], 'cost2')
            w_last = round(float(last_item['crm_kvdrmw']) - (x_last - yjkuoh_sum), 2)
            z_last = round(x_last - yjkuoh_sum, 2)
            
            self.db_update(
                'bt_crm_cost2flow',
                {
                    'crm_ccftvr': w_last,
                    'crm_yjkuoh': z_last
                },
                'cost2flow_id = %s',
                (last_item['cost2flow_id'],)
            )
    
    def find_total_by_date_and_account(self, data_list: List[Dict], id_val: str, time_val: str, data_type: str = None) -> float:
        """根据日期和账户查找总额"""
        if data_type == 'cost2':
            for value in data_list:
                if value['crm_duweee'] == time_val and value['crm_koklbg'] == id_val:
                    return float(value['total'])
        elif data_type == 'cost2flow':
            for value in data_list:
                if value['crm_ernibr'] == time_val and value['crm_wwajxo'] == id_val:
                    return float(value['total'])
        return 0.0
    
    def get_qc_cost2(self) -> Dict[str, Any]:
        """主要的千川数据更新方法"""
        # 获取最新的日志记录
        sql = "SELECT * FROM bt_crm_cost2log ORDER BY log_id DESC LIMIT 1"
        result = self.db_query(sql)
        
        if not result:
            return {'code': 1, 'msg': '没有找到日志记录'}
        
        result = result[0]
        
        # 更新千川标准流水
        cost2 = self.update_cost2(result['start'], result['end'])
        self.db_update(
            'bt_crm_cost2log',
            {'cost2': cost2['msg']},
            'log_id = %s',
            (result['log_id'],)
        )
        
        if cost2['code'] == 0:
            # 更新本地推流水
            cost2_bdt = self.update_cost2_bdt(result['start'], result['end'])
            self.db_update(
                'bt_crm_cost2log',
                {'cost2_bdt': cost2_bdt['msg']},
                'log_id = %s',
                (result['log_id'],)
            )
            
            # 更新账户是否存在全域id
            self.type_id(result['start'], result['end'])
            
            if cost2_bdt['code'] == 0:
                # 更新千川明细标准推广
                cost2flow = self.update_cost2flow(result['start'], result['end'])
                self.db_update(
                    'bt_crm_cost2log',
                    {'cost2flow': cost2flow['msg']},
                    'log_id = %s',
                    (result['log_id'],)
                )
                
                if cost2flow['code'] == 0:
                    # 更新千川明细全域推广
                    cost2flow_qy = self.update_cos2flow_qy(result['start'], result['end'])
                    self.db_update(
                        'bt_crm_cost2log',
                        {'cost2flow_qy': cost2flow_qy['msg']},
                        'log_id = %s',
                        (result['log_id'],)
                    )
                    
                    if cost2flow_qy['code'] == 0:
                        money_data = self.qc_up_data(result['start'], result['end'])
                        self.db_update(
                            'bt_crm_cost2log',
                            {
                                'cos2flow_money': money_data,
                                'status': 1
                            },
                            'log_id = %s',
                            (result['log_id'],)
                        )
                        
                        # 增加1天
                        start_date = datetime.strptime(result['start'], '%Y-%m-%d %H:%M:%S') + timedelta(days=1)
                        end_date = datetime.strptime(result['end'], '%Y-%m-%d %H:%M:%S') + timedelta(days=1)
                        
                        new_start = start_date.strftime('%Y-%m-%d %H:%M:%S')
                        new_end = end_date.strftime('%Y-%m-%d %H:%M:%S')
                        
                        # 插入新的日志记录
                        self.db_insert('bt_crm_cost2log', {
                            'start': new_start,
                            'end': new_end,
                            'status': '0',
                        })
                        
                        # 发送成功消息
                        fs_message = {
                            'time': f"{result['start']},{result['end']}",
                            'type': '千川数据更新成功'
                        }
                        self.to_message('18148497665', fs_message, 'AAqjPV3C50X8w')
                        
                        return {'code': 0, 'msg': '千川数据更新成功'}
                    else:
                        # 发送失败消息
                        fs_message = {
                            'time': f"{result['start']},{result['end']}",
                            'type': '千川数据更新失败'
                        }
                        
                        # 失败后删除数据
                        date = datetime.strptime(result['start'], '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
                        self.db_delete('bt_crm_cost2', 'crm_duweee >= %s', (date,))
                        self.db_delete('bt_crm_cost2flow', 'crm_ernibr >= %s', (date,))
                        
                        self.to_message('18148497665', fs_message, 'AAqjPV3C50X8w')
                        return {'code': 1, 'msg': '千川数据更新失败'}
                else:
                    # 发送失败消息
                    fs_message = {
                        'time': f"{result['start']},{result['end']}",
                        'type': cost2flow['msg']
                    }
                    
                    date = datetime.strptime(result['start'], '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
                    self.db_delete('bt_crm_cost2', 'crm_duweee >= %s', (date,))
                    self.db_delete('bt_crm_cost2flow', 'crm_ernibr >= %s', (date,))
                    
                    self.to_message('18148497665', fs_message, 'AAqjPV3C50X8w')
                    return {'code': 1, 'msg': cost2flow['msg']}
            else:
                # 发送失败消息
                fs_message = {
                    'time': f"{result['start']},{result['end']}",
                    'type': cost2_bdt['msg']
                }
                
                date = datetime.strptime(result['start'], '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
                self.db_delete('bt_crm_cost2', 'crm_duweee >= %s', (date,))
                self.db_delete('bt_crm_cost2flow', 'crm_ernibr >= %s', (date,))
                
                self.to_message('18148497665', fs_message, 'AAqjPV3C50X8w')
                return {'code': 1, 'msg': cost2_bdt['msg']}
        else:
            # 发送失败消息
            fs_message = {
                'time': f"{result['start']},{result['end']}",
                'type': cost2['msg']
            }
            
            date = datetime.strptime(result['start'], '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            self.db_delete('bt_crm_cost2', 'crm_duweee >= %s', (date,))
            self.db_delete('bt_crm_cost2flow', 'crm_ernibr >= %s', (date,))
            
            self.to_message('18148497665', fs_message, 'AAqjPV3C50X8w')
            return {'code': 1, 'msg': cost2['msg']}


def main():
    """主函数"""
    # 数据库配置
    db_config = {
        'hostname': '127.0.0.1',
        'username': 'root',
        'password': 'root',
        'database': 'bt_crm',
        'hostport': 3306,
        'charset': 'utf8mb4'
    }
    
    # 创建更新器实例
    updater = QcCost2Updater(db_config)
    
    try:
        # 执行千川数据更新
        result = updater.get_qc_cost2()
        print(f"更新结果: {result}")
        
        if result['code'] == 0:
            print("✅ 千川数据更新成功")
        else:
            print(f"❌ 千川数据更新失败: {result['msg']}")
            
    except Exception as e:
        print(f"❌ 执行过程中发生错误: {str(e)}")


if __name__ == "__main__":
    main() 