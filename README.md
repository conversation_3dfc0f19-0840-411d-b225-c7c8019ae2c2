## 主要技术栈

后端框架：ThinkPHP 5.0.24

前端MVVM框架：Vue.JS 2.5.x

路由：Vue-Router 3.x

数据交互：Axios

UI框架：Element-UI 2.6.3

白兔crm11.0的运行环境要求PHP7.0以上

其他技术栈：Redis


### 数据交互

数据交互通过axios以及RESTful架构来实现
用户校验通过登录返回的auth_key放在header
值得注意的一点是：跨域的情况下，会有预请求OPTION的情况

### Server搭建

服务端使用的框架为thinkphp5.0.24，搭建前请确保拥有lamp/lnmp/wamp环境。

这里所说的搭建其实就是把server框架放入WEB运行环境，并使用80端口。
导入服务端根文件夹数据库文件public/sql/btcrm.sql，并修改config/database.php配置文件。

### 配置要求

PHP >= 7.0.*
当访问 http://localhost/, 出现"白兔软件"即代表后端接口搭建成功。

### 前端部署

安装node.js 前端部分是基于node.js上运行的，所以必须先安装`node.js`，版本要求为6.0以上（目前node版本号为8.17.0，64位）

使用npm安装依赖 下载白兔CRM11.0前端代码； 可将代码放置在后端同级目录frontend，执行命令安装依赖：

npm install
修改内部配置 修改请求地址或域名：config/dev.env.js里修改BASE_API（开发环境服务端地址，默认localhost） 修改自定义端口：config/index.js里面的dev对象的port参数（默认8080，不建议修改）

### 运行前端

npm run dev
注意：前端服务启动，默认会占用8080端口，所以在启动前端服务之前，请确认8080端口没有被占用。
程序运行之前需搭建好Server端

# 千川数据更新脚本

这是一个将PHP的`getQcCost2`方法及其相关方法转换为Python的脚本。

## 功能说明

该脚本主要实现以下功能：

1. **千川标准流水更新** (`update_cost2`) - 从千川API获取标准推广数据
2. **本地推流水更新** (`update_cost2_bdt`) - 更新本地推广数据
3. **明细标准推广更新** (`update_cost2flow`) - 更新标准推广明细数据
4. **明细全域推广更新** (`update_cos2flow_qy`) - 更新全域推广明细数据
5. **赠款数据更新** (`qc_up_data`) - 处理赠款相关数据
6. **飞书消息通知** (`to_message`) - 发送更新状态通知

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

在使用脚本前，需要修改`main()`函数中的数据库配置：

```python
db_config = {
    'hostname': '127.0.0.1',  # 数据库主机地址
    'username': 'root',       # 数据库用户名
    'password': 'root',       # 数据库密码
    'database': 'bt_crm',     # 数据库名称
    'hostport': 3306,         # 数据库端口
    'charset': 'utf8mb4'      # 字符集
}
```

## 使用方法

直接运行脚本：

```bash
python qc_cost2_updater.py
```

## 主要方法说明

### `get_qc_cost2()`
主要的千川数据更新方法，按以下顺序执行：
1. 获取最新的日志记录
2. 更新千川标准流水
3. 更新本地推流水
4. 更新账户全域ID状态
5. 更新千川明细标准推广
6. 更新千川明细全域推广
7. 更新赠款数据
8. 发送通知消息

### `update_cost2(start, end)`
更新千川标准流水数据，从千川API获取推广数据并存储到数据库。

### `update_cost2flow(start, end)`
更新千川明细标准推广数据，包含重试机制和限流处理。

### `update_cos2flow_qy(start, end)`
更新千川明细全域推广数据。

### `qc_up_data(start, end)`
处理赠款数据，计算各种推广类型的赠款分配。

### `to_message(phone, shop, card)`
发送飞书消息通知，支持不同类型的消息模板。

## 注意事项

1. 确保数据库连接配置正确
2. 确保千川API的访问令牌有效
3. 确保飞书API的租户访问令牌有效
4. 脚本包含重试机制和限流处理，适合处理大量数据
5. 所有数据库操作都使用参数化查询，防止SQL注入

## 错误处理

脚本包含完善的错误处理机制：
- API请求失败时会重试
- 数据库操作失败时会回滚
- 更新失败时会删除已插入的数据
- 会发送失败通知消息

## 日志记录

脚本会在控制台输出执行过程中的关键信息，包括：
- 请求延迟信息
- 重试次数
- 成功/失败状态
- 错误信息
